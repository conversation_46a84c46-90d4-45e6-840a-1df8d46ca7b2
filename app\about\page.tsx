import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Award, BookOpen, Calendar, Compass, Heart, Sparkles, Star, Target, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function About() {
	const timeline = [
		{
			year: "2012",
			title: "Découverte des soins énergétiques",
			description: "Première formation en Reiki et découverte de ma sensibilité aux énergies subtiles.",
		},
		{
			year: "2015",
			title: "Certification praticienne",
			description: "Obtention de ma certification en soins énergétiques et début de ma pratique professionnelle.",
		},
		{
			year: "2018",
			title: "Formation en coaching",
			description: "Spécialisation en coaching de vie et PNL pour enrichir mon accompagnement.",
		},
		{
			year: "2020",
			title: "Expertise en libération émotionnelle",
			description: "Formation avancée en techniques de libération des blocages émotionnels.",
		},
		{
			year: "2023",
			title: "Plus de 500 accompagnements",
			description: "Milestone important avec une clientèle fidèle et des résultats durables.",
		},
	];

	const certifications = [
		{
			title: "Praticienne Reiki Usui",
			organization: "Fédération Française de Reiki",
			year: "2015",
		},
		{
			title: "Coach certifiée PNL",
			organization: "Institut Français de PNL",
			year: "2018",
		},
		{
			title: "Spécialiste EFT",
			organization: "EFT France",
			year: "2020",
		},
		{
			title: "Harmonisation des Chakras",
			organization: "École Européenne d'Énergétique",
			year: "2021",
		},
	];

	const values = [
		{
			icon: <Heart className="w-6 h-6 text-brand-gold" />,
			title: "Bienveillance",
			description: "Chaque personne est accueillie avec respect et sans jugement, dans un espace sécurisé.",
		},
		{
			icon: <Target className="w-6 h-6 text-brand-gold" />,
			title: "Efficacité",
			description: "Des méthodes éprouvées et personnalisées pour des résultats concrets et durables.",
		},
		{
			icon: <Sparkles className="w-6 h-6 text-brand-gold" />,
			title: "Authenticité",
			description: "Une approche sincère qui honore votre unicité et respecte votre rythme.",
		},
		{
			icon: <Compass className="w-6 h-6 text-brand-gold" />,
			title: "Guidance",
			description: "Un accompagnement qui vous aide à trouver vos propres réponses et votre voie.",
		},
	];

	return (
		<div className="pt-20 bg-gradient-to-br from-brand-cream via-white to-brand-sand/30 relative min-h-screen">
			{/* Global background pattern */}
			<div className="fixed inset-0 opacity-15 pointer-events-none">
				<div className="absolute top-1/4 left-1/4 w-96 h-96 bg-brand-gold rounded-full blur-3xl animate-gentle-pulse"></div>
				<div
					className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-brand-blue rounded-full blur-3xl animate-gentle-pulse"
					style={{ animationDelay: "2s" }}
				></div>
				<div
					className="absolute top-1/2 left-1/2 w-64 h-64 bg-brand-blue-variant rounded-full blur-2xl animate-gentle-pulse -translate-x-1/2 -translate-y-1/2"
					style={{ animationDelay: "4s" }}
				></div>
				<div
					className="absolute top-10 right-10 w-32 h-32 bg-brand-gold rounded-full blur-2xl animate-gentle-pulse"
					style={{ animationDelay: "1s" }}
				></div>
				<div
					className="absolute bottom-10 left-10 w-40 h-40 bg-brand-blue rounded-full blur-3xl animate-gentle-pulse"
					style={{ animationDelay: "3s" }}
				></div>
			</div>

			{/* Hero Section */}
			<section className="py-20 relative">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<div className="grid lg:grid-cols-2 gap-12 items-center">
						<div className="text-center lg:text-left">
							<div className="inline-flex items-center px-6 py-3 rounded-full bg-brand-gold/15 border-2 border-brand-gold/30 mb-8 shadow-lg backdrop-blur-sm">
								<Heart className="w-5 h-5 text-brand-gold mr-3" />
								<span className="text-lg font-dm-serif text-brand-gold font-bold">Mon histoire</span>
							</div>

							<h1 className="font-new-york text-4xl md:text-5xl font-bold text-brand-olive mb-6">
								Laurence Gémin
							</h1>

							<p className="font-crimson text-xl text-brand-gold italic mb-6">
								"Accompagner chaque être vers sa lumière intérieure"
							</p>

							<p className="font-doulos text-xl text-brand-olive/80 leading-relaxed mb-8">
								Passionnée par l'accompagnement humain depuis plus de 10 ans, j'ai fait de ma
								sensibilité aux énergies subtiles un véritable outil de transformation pour aider les
								autres à retrouver leur équilibre et révéler leur potentiel authentique.
							</p>

							<div className="flex flex-col sm:flex-row flex-wrap gap-4 justify-center lg:justify-start items-center">
								<Button
									asChild
									size="lg"
									className="bg-brand-gold hover:bg-brand-gold-dark text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group text-lg shadow-lg"
								>
									<Link href="/reservation" className="flex items-center">
										Prendre rendez-vous
										<ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
									</Link>
								</Button>

								<Button
									asChild
									variant="outline"
									size="lg"
									className="border-2 border-brand-blue text-brand-blue bg-brand-cream hover:bg-brand-blue hover:text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-lg text-lg backdrop-blur-sm bg-white/50"
								>
									<Link href="/services">Mes services</Link>
								</Button>
							</div>
						</div>

						<div className="relative">
							<div className="aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-brand-sand/20 to-brand-gold/15 border-2 border-white/20 backdrop-blur-sm shadow-2xl">
								<Image
									src="/placeholder.svg?key=about-hero"
									alt="Laurence Gémin - Coach en soins énergétiques"
									width={600}
									height={600}
									className="object-cover w-full h-full"
								/>
							</div>
							<div className="absolute -bottom-6 -right-6 w-24 h-24 bg-brand-gold/20 backdrop-blur-sm rounded-full flex items-center justify-center shadow-xl border-2 border-white/30">
								<Sparkles className="w-8 h-8 text-brand-gold animate-pulse" />
							</div>
							<div className="absolute -top-6 -left-6 w-16 h-16 bg-brand-cream/80 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg border-2 border-white/30">
								<Star className="w-6 h-6 text-brand-gold" />
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* Story Section */}
			<section className="py-20 relative">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-brand-olive mb-6">
							Mon chemin vers les soins énergétiques
						</h2>
						<p className="font-doulos text-xl text-brand-olive/80 leading-relaxed">
							Découvrez comment ma propre quête de bien-être m'a menée à accompagner les autres dans leur
							transformation personnelle.
						</p>
					</div>

					<div className="prose prose-lg max-w-none">
						<div className="space-y-10">
							<Card className="group bg-gradient-to-br from-white via-white to-brand-sand/10 border-2 border-brand-sand/30 hover:border-brand-gold/50 transition-all duration-300 hover:shadow-2xl hover:-translate-y-3 backdrop-blur-sm hover:bg-gradient-to-br hover:from-white hover:via-brand-gold/5 hover:to-brand-gold/10">
								<CardContent className="p-10">
									<div className="w-16 h-16 mb-6 bg-gradient-to-br from-brand-gold/20 to-brand-gold/10 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-xl border border-brand-gold/20">
										<BookOpen className="w-8 h-8 text-brand-gold" />
									</div>
									<h3 className="font-new-york text-xl font-semibold text-brand-olive mb-6">
										Les prémices d'une vocation
									</h3>
									<p className="font-doulos text-xl text-brand-olive/80 leading-relaxed">
										Tout a commencé par une période difficile de ma vie où je cherchais des réponses
										au-delà de la médecine traditionnelle. C'est lors d'une séance de Reiki que j'ai
										découvert ma sensibilité particulière aux énergies subtiles et ressenti un appel
										profond à aider les autres à travers cette approche holistique.
									</p>
								</CardContent>
							</Card>

							<Card className="group bg-gradient-to-br from-white via-white to-brand-sand/10 border-2 border-brand-sand/30 hover:border-brand-gold/50 transition-all duration-300 hover:shadow-2xl hover:-translate-y-3 backdrop-blur-sm hover:bg-gradient-to-br hover:from-white hover:via-brand-gold/5 hover:to-brand-gold/10">
								<CardContent className="p-10">
									<div className="w-16 h-16 mb-6 bg-gradient-to-br from-brand-gold/20 to-brand-gold/10 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-xl border border-brand-gold/20">
										<Sparkles className="w-8 h-8 text-brand-gold" />
									</div>
									<h3 className="font-new-york text-xl font-semibold text-brand-olive mb-6">
										Formation et développement
									</h3>
									<p className="font-doulos text-xl text-brand-olive/80 leading-relaxed">
										J'ai alors entrepris un parcours de formation rigoureux, multipliant les
										apprentissages auprès de maîtres reconnus. Reiki, PNL, EFT, harmonisation des
										chakras... Chaque technique apprise enrichit ma palette d'outils pour mieux
										accompagner chaque personne selon ses besoins spécifiques.
									</p>
								</CardContent>
							</Card>

							<Card className="group bg-gradient-to-br from-white via-white to-brand-sand/10 border-2 border-brand-sand/30 hover:border-brand-gold/50 transition-all duration-300 hover:shadow-2xl hover:-translate-y-3 backdrop-blur-sm hover:bg-gradient-to-br hover:from-white hover:via-brand-gold/5 hover:to-brand-gold/10">
								<CardContent className="p-10">
									<div className="w-16 h-16 mb-6 bg-gradient-to-br from-brand-gold/20 to-brand-gold/10 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-xl border border-brand-gold/20">
										<Heart className="w-8 h-8 text-brand-gold" />
									</div>
									<h3 className="font-new-york text-xl font-semibold text-brand-olive mb-6">
										Une mission de vie
									</h3>
									<p className="font-doulos text-xl text-brand-olive/80 leading-relaxed">
										Aujourd'hui, après plus de 500 accompagnements, je sais que ma mission est
										d'aider chaque personne à retrouver sa lumière intérieure. Chaque séance est
										unique, chaque parcours est respecté, et chaque transformation me rappelle
										pourquoi j'ai choisi cette voie magnifique.
									</p>
								</CardContent>
							</Card>
						</div>
					</div>
				</div>
			</section>

			{/* Timeline Section */}
			<section className="py-20 relative">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-brand-olive mb-6">
							Mon parcours professionnel
						</h2>
						<p className="font-doulos text-xl text-brand-olive/80">
							Les étapes clés qui ont façonné mon expertise
						</p>
					</div>

					<div className="relative">
						{/* Timeline Line */}
						<div className="absolute left-8 top-0 bottom-0 w-0.5 bg-brand-gold/30"></div>

						<div className="space-y-12">
							{timeline.map((item, index) => (
								<div key={index} className="relative flex items-start">
									<div className="flex-shrink-0 w-16 h-16 bg-brand-gold rounded-full flex items-center justify-center text-white font-bold text-xl font-new-york relative z-10 shadow-lg">
										{item.year.slice(-2)}
									</div>
									<div className="ml-8 flex-1">
										<Card className="group bg-gradient-to-br from-white via-white to-brand-sand/10 border-2 border-brand-sand/30 hover:border-brand-gold/50 transition-all duration-300 hover:shadow-2xl hover:-translate-y-3 backdrop-blur-sm hover:bg-gradient-to-br hover:from-white hover:via-brand-gold/5 hover:to-brand-gold/10">
											<CardContent className="p-8">
												<div className="flex items-center justify-between mb-4">
													<h3 className="font-new-york text-xl font-semibold text-brand-olive">
														{item.title}
													</h3>
													<span className="text-lg font-sans text-brand-gold font-bold">
														{item.year}
													</span>
												</div>
												<p className="font-doulos text-xl text-brand-olive/80 leading-relaxed">
													{item.description}
												</p>
											</CardContent>
										</Card>
									</div>
								</div>
							))}
						</div>
					</div>
				</div>
			</section>

			{/* Certifications Section */}
			<section className="py-20 relative">
				<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-brand-olive mb-6">
							Certifications & Formations
						</h2>
						<p className="font-doulos text-xl text-brand-olive/80">
							Une expertise reconnue et en constante évolution
						</p>
					</div>

					<div className="grid md:grid-cols-2 gap-6">
						{certifications.map((cert, index) => (
							<Card
								key={index}
								className="group bg-gradient-to-br from-white via-white to-brand-sand/10 border-2 border-brand-sand/30 hover:border-brand-gold/50 transition-all duration-300 hover:shadow-2xl hover:-translate-y-3 backdrop-blur-sm hover:bg-gradient-to-br hover:from-white hover:via-brand-gold/5 hover:to-brand-gold/10"
							>
								<CardContent className="p-6">
									<div className="flex items-start space-x-4">
										<div className="w-12 h-12 bg-gradient-to-br from-brand-gold/20 to-brand-gold/10 rounded-full flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300 shadow-lg border border-brand-gold/20">
											<Award className="w-6 h-6 text-brand-gold" />
										</div>
										<div className="flex-1">
											<h3 className="font-new-york text-lg font-semibold text-brand-olive mb-1">
												{cert.title}
											</h3>
											<p className="font-sans text-brand-olive/70 text-base mb-2">
												{cert.organization}
											</p>
											<span className="inline-flex items-center px-3 py-1 rounded-full bg-brand-gold/15 border border-brand-gold/30 text-brand-gold text-sm font-medium">
												{cert.year}
											</span>
										</div>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Values Section */}
			<section className="py-20 relative">
				<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-brand-olive mb-6">
							Mes valeurs fondamentales
						</h2>
						<p className="font-doulos text-xl text-brand-olive/80">
							Les principes qui guident chacun de mes accompagnements
						</p>
					</div>

					<div className="grid md:grid-cols-2 gap-8">
						{values.map((value, index) => (
							<Card
								key={index}
								className="group bg-gradient-to-br from-white via-white to-brand-sand/10 border-2 border-brand-sand/30 hover:border-brand-gold/50 transition-all duration-300 hover:shadow-2xl hover:-translate-y-3 backdrop-blur-sm hover:bg-gradient-to-br hover:from-white hover:via-brand-gold/5 hover:to-brand-gold/10"
							>
								<CardContent className="p-8 text-center">
									<div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-brand-gold/20 to-brand-gold/10 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-xl border border-brand-gold/20">
										<div className="text-brand-gold">{value.icon}</div>
									</div>
									<h3 className="font-new-york text-xl font-semibold text-brand-olive mb-4">
										{value.title}
									</h3>
									<p className="font-doulos text-lg text-brand-olive/80 leading-relaxed">
										{value.description}
									</p>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Stats Section */}
			<section className="py-20 relative">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<div className="grid md:grid-cols-3 gap-8 text-center">
						<div className="group">
							<div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-brand-gold/15 to-brand-gold/10 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
								<Users className="w-10 h-10 text-brand-gold" />
							</div>
							<div className="font-new-york text-3xl font-bold text-brand-olive mb-2">500+</div>
							<p className="font-doulos text-brand-olive/70">Accompagnements réalisés</p>
						</div>

						<div className="group">
							<div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-brand-gold/15 to-brand-gold/10 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
								<Calendar className="w-10 h-10 text-brand-gold" />
							</div>
							<div className="font-new-york text-3xl font-bold text-brand-olive mb-2">10+</div>
							<p className="font-doulos text-brand-olive/70">Années d'expérience</p>
						</div>

						<div className="group">
							<div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-brand-gold/15 to-brand-gold/10 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
								<Star className="w-10 h-10 text-brand-gold" />
							</div>
							<div className="font-new-york text-3xl font-bold text-brand-olive mb-2">98%</div>
							<p className="font-doulos text-brand-olive/70">Clients satisfaits</p>
						</div>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-20 relative">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
					<div className="bg-white/95 backdrop-blur-md rounded-3xl p-8 md:p-12 shadow-2xl border-2 border-brand-gold/30 hover:border-brand-gold/40 transition-all duration-300">
						<div className="w-20 h-20 mx-auto mb-8 bg-gradient-to-br from-brand-gold/20 to-brand-gold/10 rounded-full flex items-center justify-center shadow-lg">
							<Heart className="w-12 h-12 text-brand-gold" />
						</div>

						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-brand-olive mb-4">
							Prêt(e) à commencer votre transformation ?
						</h2>

						<p className="font-doulos text-xl text-brand-olive/80 mb-8 max-w-2xl mx-auto">
							Chaque parcours est unique. Offrons-nous un premier échange pour découvrir comment je peux
							vous accompagner vers votre mieux-être.
						</p>

						<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
							<Button
								asChild
								size="lg"
								className="bg-brand-gold hover:bg-brand-gold-dark text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group text-lg shadow-lg"
							>
								<Link href="/reservation" className="flex items-center">
									Prendre rendez-vous
									<ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
								</Link>
							</Button>

							<Button
								asChild
								variant="outline"
								size="lg"
								className="border-2 border-brand-blue text-brand-blue hover:bg-brand-blue hover:text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-lg text-lg backdrop-blur-sm bg-white/50"
							>
								<Link href="/testimonials">Lire les témoignages</Link>
							</Button>
						</div>
					</div>
				</div>
			</section>
		</div>
	);
}

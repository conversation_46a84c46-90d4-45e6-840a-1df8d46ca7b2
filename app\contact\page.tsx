import ContactForm from "@/components/ContactForm";
import FAQSection from "@/components/FAQSection";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Calendar, Clock, Heart, MapPin, MessageCircle, Phone } from "lucide-react";
import Link from "next/link";

export default function Contact() {
	return (
		<div className="pt-20 bg-gradient-to-br from-brand-cream via-white to-brand-sand/30 relative min-h-screen">
			{/* Global background pattern */}
			<div className="fixed inset-0 opacity-15 pointer-events-none">
				<div className="absolute top-1/4 left-1/4 w-96 h-96 bg-brand-gold rounded-full blur-3xl animate-gentle-pulse"></div>
				<div
					className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-brand-blue rounded-full blur-3xl animate-gentle-pulse"
					style={{ animationDelay: "2s" }}
				></div>
				<div
					className="absolute top-1/2 left-1/2 w-64 h-64 bg-brand-blue-variant rounded-full blur-2xl animate-gentle-pulse -translate-x-1/2 -translate-y-1/2"
					style={{ animationDelay: "4s" }}
				></div>
				<div
					className="absolute top-10 right-10 w-32 h-32 bg-brand-gold rounded-full blur-2xl animate-gentle-pulse"
					style={{ animationDelay: "1s" }}
				></div>
				<div
					className="absolute bottom-10 left-10 w-40 h-40 bg-brand-blue rounded-full blur-3xl animate-gentle-pulse"
					style={{ animationDelay: "3s" }}
				></div>
			</div>

			{/* Hero Section */}
			<section className="py-20 relative">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<div className="inline-flex items-center px-6 py-3 rounded-full bg-brand-gold/15 border-2 border-brand-gold/30 mb-8 shadow-lg">
						<MessageCircle className="w-5 h-5 text-brand-gold mr-3" />
						<span className="text-lg font-dm-serif text-brand-gold font-bold">Contact</span>
					</div>

					<h1 className="font-new-york text-4xl md:text-5xl font-bold text-brand-olive mb-6">
						Prenons rendez-vous
					</h1>

					<p className="font-doulos text-xl text-brand-olive/80 leading-relaxed mb-8 max-w-2xl mx-auto">
						Je serais ravie d'échanger avec vous sur vos besoins et de vous accompagner dans votre parcours
						vers le mieux-être. Contactez-moi pour planifier votre première séance ou simplement pour poser
						vos questions.
					</p>

					<div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-brand-sand/30 inline-block">
						<div className="flex items-center justify-center space-x-6 text-lg font-doulos text-brand-olive/80">
							<div className="flex items-center">
								<Phone className="w-4 h-4 text-brand-gold mr-2" />
								<span>Premier échange gratuit</span>
							</div>
							<div className="flex items-center">
								<Calendar className="w-4 h-4 text-brand-gold mr-2" />
								<span>Réponse sous 24h</span>
							</div>
							<div className="flex items-center">
								<Heart className="w-4 h-4 text-brand-gold mr-2" />
								<span>Accompagnement bienveillant</span>
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* Contact Form Section */}
			<section className="py-20 relative">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<ContactForm />
				</div>
			</section>

			{/* Additional Info */}
			<section className="py-10 relative">
				<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-brand-olive mb-4">
							Informations pratiques
						</h2>
						<p className="font-doulos text-xl text-brand-olive/80">
							Tout ce que vous devez savoir avant notre première rencontre
						</p>
					</div>

					<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
						<Card className="group bg-gradient-to-br from-white via-white to-brand-sand/10 border-2 border-brand-sand/30 hover:border-brand-gold/50 transition-all duration-300 hover:shadow-2xl hover:-translate-y-3 backdrop-blur-sm hover:bg-gradient-to-br hover:from-white hover:via-brand-gold/5 hover:to-brand-gold/10">
							<CardHeader>
								<div className="w-16 h-16 bg-gradient-to-br from-brand-gold/20 to-brand-gold/10 rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-xl border border-brand-gold/20">
									<Clock className="w-8 h-8 text-brand-gold" />
								</div>
								<CardTitle className="font-new-york text-lg text-brand-olive">Horaires</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-3 font-sans text-brand-olive/80 text-lg">
									<div className="flex justify-between">
										<span>Lundi - Vendredi</span>
										<span>9h00 - 19h00</span>
									</div>
									<div className="flex justify-between">
										<span>Samedi</span>
										<span>9h00 - 17h00</span>
									</div>
									<div className="flex justify-between">
										<span>Dimanche</span>
										<span>Fermé</span>
									</div>
									<div className="pt-3 border-t border-brand-sand/30">
										<p className="text-lg text-brand-olive/60">
											Horaires flexibles selon vos contraintes
										</p>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card className="group bg-gradient-to-br from-white via-white to-brand-sand/10 border-2 border-brand-sand/30 hover:border-brand-gold/50 transition-all duration-300 hover:shadow-2xl hover:-translate-y-3 backdrop-blur-sm hover:bg-gradient-to-br hover:from-white hover:via-brand-gold/5 hover:to-brand-gold/10">
							<CardHeader>
								<div className="w-16 h-16 bg-gradient-to-br from-brand-gold/20 to-brand-gold/10 rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-xl border border-brand-gold/20">
									<MapPin className="w-8 h-8 text-brand-gold" />
								</div>
								<CardTitle className="font-new-york text-lg text-brand-olive">Modalités</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-3 font-sans text-brand-olive/80 text-lg">
									<div>
										<h4 className="font-semibold text-brand-olive mb-1">En cabinet</h4>
										<p>Séances en présentiel dans un cadre chaleureux et apaisant</p>
									</div>
									<div>
										<h4 className="font-semibold text-brand-olive mb-1">À distance</h4>
										<p>Soins énergétiques par téléphone ou visioconférence</p>
									</div>
									<div>
										<h4 className="font-semibold text-brand-olive mb-1">À domicile</h4>
										<p>Possible selon la localisation (supplément appliqué)</p>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card className="group bg-gradient-to-br from-white via-white to-brand-sand/10 border-2 border-brand-sand/30 hover:border-brand-gold/50 transition-all duration-300 hover:shadow-2xl hover:-translate-y-3 backdrop-blur-sm hover:bg-gradient-to-br hover:from-white hover:via-brand-gold/5 hover:to-brand-gold/10">
							<CardHeader>
								<div className="w-16 h-16 bg-gradient-to-br from-brand-gold/20 to-brand-gold/10 rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-xl border border-brand-gold/20">
									<MessageCircle className="w-8 h-8 text-brand-gold" />
								</div>
								<CardTitle className="font-new-york text-lg text-brand-olive">
									Premier contact
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-3 font-sans text-brand-olive/80 text-lg">
									<div>
										<h4 className="font-semibold text-brand-olive mb-1">Échange gratuit</h4>
										<p>15-20 minutes pour faire connaissance et répondre à vos questions</p>
									</div>
									<div>
										<h4 className="font-semibold text-brand-olive mb-1">Sans engagement</h4>
										<p>Vous décidez ensuite si vous souhaitez poursuivre</p>
									</div>
									<div>
										<h4 className="font-semibold text-brand-olive mb-1">Personnalisé</h4>
										<p>Nous définissons ensemble l'approche qui vous convient</p>
									</div>
								</div>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>

			{/* FAQ Section */}
			<section className="py-10 relative">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<FAQSection
						faqs={[
							{
								question: "Comment se déroule le premier rendez-vous ?",
								answer: "Nous commençons toujours par un échange téléphonique gratuit de 15-20 minutes pour faire connaissance, comprendre vos besoins et répondre à vos questions. Si vous souhaitez poursuivre, nous planifions ensuite votre première séance selon vos disponibilités.",
							},
							{
								question: "Quels sont vos tarifs ?",
								answer: "Mes tarifs varient selon le type de séance : 60€ pour un soin à distance, 65€ pour un coaching bien-être, 70-85€ pour les soins énergétiques complets. Le premier échange téléphonique est toujours gratuit. Des forfaits sont disponibles pour un suivi régulier.",
							},
							{
								question: "Acceptez-vous les paiements échelonnés ?",
								answer: "Oui, je comprends que l'investissement dans son bien-être puisse représenter un budget. Nous pouvons discuter ensemble de modalités de paiement adaptées à votre situation. L'important est que vous puissiez accéder aux soins dont vous avez besoin.",
							},
							{
								question: "Combien de temps dure une séance ?",
								answer: "La durée varie selon le type de soin : 1h pour un coaching ou un soin à distance, 1h15 pour une libération émotionnelle, 1h30 pour un soin énergétique complet. Je prends toujours le temps nécessaire, sans vous presser.",
							},
							{
								question: "Les soins à distance sont-ils vraiment efficaces ?",
								answer: "Absolument ! L'énergie n'a pas de frontières physiques. Mes clients à distance obtiennent les mêmes résultats qu'en présentiel. La connexion énergétique se fait naturellement, et vous bénéficiez du confort de votre domicile.",
							},
							{
								question: "Puis-je annuler ou reporter un rendez-vous ?",
								answer: "Bien sûr, je comprends que les imprévus arrivent. Il suffit de me prévenir au moins 24h à l'avance pour reporter sans frais. En cas d'urgence, nous trouvons toujours une solution ensemble.",
							},
						]}
						description="Les réponses aux questions que vous vous posez avant de prendre rendez-vous"
					/>
				</div>
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center mb-20">
					<Link
						href="/reservation"
						className="flex items-center justify-center bg-brand-gold hover:bg-brand-gold-dark text-white py-4 px-10 rounded-full font-sans font-bold transition-colors duration-300 group text-lg shadow-lg hover:shadow-xl hover:scale-105"
					>
						Prendre rendez-vous
						<ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
					</Link>
				</div>
			</section>
		</div>
	);
}

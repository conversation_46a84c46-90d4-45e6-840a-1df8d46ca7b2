﻿@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 1.125rem; /* 18px base instead of 16px */
  line-height: 1.75;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    /* Brand color scheme - proper HSL values */
    --background: 0 0% 100%; /* pure white background */
    --foreground: 210 22% 22%; /* brand-olive #2c3e50 */
    --card: 0 0% 100%; /* pure white for cards */
    --card-foreground: 210 22% 22%; /* brand-olive #2c3e50 */
    --popover: 0 0% 100%; /* pure white for popovers */
    --popover-foreground: 210 22% 22%; /* brand-olive #2c3e50 */
    --primary: 43 69% 57%; /* brand-gold #d8af4c */
    --primary-foreground: 0 0% 100%;
    --secondary: 197 61% 53%; /* brand-blue #36b0d8 */
    --secondary-foreground: 0 0% 100%;
    --muted: 210 17% 97%; /* brand-sand #f8f9fa */
    --muted-foreground: 213 32% 59%; /* brand-blue-variant #4d92d4 */
    --accent: 213 32% 59%; /* brand-blue-variant #4d92d4 */
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%; /* light border */
    --input: 0 0% 89.8%; /* input border */
    --ring: 43 69% 57%; /* brand-gold #d8af4c */
    --chart-1: 43 69% 57%; /* brand-gold */
    --chart-2: 197 61% 53%; /* brand-blue */
    --chart-3: 213 32% 59%; /* brand-blue-variant */
    --chart-4: 210 17% 97%; /* brand-sand */
    --chart-5: 43 69% 35%; /* brand-gold-dark */
    --radius: 0.5rem;
    --sidebar-background: 0 0% 99.6%; /* brand-cream */
    --sidebar-foreground: 210 22% 22%; /* brand-olive */
    --sidebar-primary: 43 69% 57%; /* brand-gold */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 213 32% 59%; /* brand-blue-variant */
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 0 0% 89.8%;
    --sidebar-ring: 43 69% 57%; /* brand-gold */
  }

  .dark {
    /* Dark mode with brand colors */
    --background: 0 0% 3.9%; /* dark background */
    --foreground: 0 0% 98%; /* light text */
    --card: 0 0% 3.9%; /* dark card */
    --card-foreground: 0 0% 98%; /* light text */
    --popover: 0 0% 3.9%; /* dark popover */
    --popover-foreground: 0 0% 98%; /* light text */
    --primary: 43 69% 57%; /* brand-gold #d8af4c */
    --primary-foreground: 0 0% 9%;
    --secondary: 197 61% 53%; /* brand-blue #36b0d8 */
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%; /* dark muted */
    --muted-foreground: 0 0% 63.9%; /* muted text */
    --accent: 213 32% 59%; /* brand-blue-variant #4d92d4 */
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%; /* dark border */
    --input: 0 0% 14.9%; /* dark input */
    --ring: 43 69% 57%; /* brand-gold */
    --chart-1: 43 69% 57%; /* brand-gold */
    --chart-2: 197 61% 53%; /* brand-blue */
    --chart-3: 213 32% 59%; /* brand-blue-variant */
    --chart-4: 0 0% 14.9%; /* dark muted */
    --chart-5: 43 69% 35%; /* brand-gold-dark */
    --sidebar-background: 240 5.9% 10%; /* dark sidebar */
    --sidebar-foreground: 240 4.8% 95.9%; /* light text */
    --sidebar-primary: 43 69% 57%; /* brand-gold */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 213 32% 59%; /* brand-blue-variant */
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 43 69% 57%; /* brand-gold */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-gradient-to-br from-brand-cream via-white to-brand-sand/30 text-foreground;
    min-height: 100vh;
  }
}

/* Custom animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gentle-pulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes float-rotate {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
  }
}

@keyframes drift {
  0%, 100% {
    transform: translateX(0px) translateY(0px);
  }
  25% {
    transform: translateX(10px) translateY(-5px);
  }
  50% {
    transform: translateX(-5px) translateY(-10px);
  }
  75% {
    transform: translateX(-10px) translateY(-5px);
  }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(216, 175, 76, 0.3);
    opacity: 0.8;
  }
  50% {
    box-shadow: 0 0 20px rgba(216, 175, 76, 0.6);
    opacity: 1;
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out;
}

.animate-gentle-pulse {
  animation: gentle-pulse 4s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(216, 175, 76, 0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

.animate-float-rotate {
  animation: float-rotate 6s ease-in-out infinite;
}

.animate-drift {
  animation: drift 8s ease-in-out infinite;
}

.animate-glow-pulse {
  animation: glow-pulse 3s ease-in-out infinite;
}

/* Enhanced glass morphism effect */
.glass-effect {
  backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(216, 175, 76, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Premium card hover effects */
.premium-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(216, 175, 76, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1);
}

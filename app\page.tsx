import Hero from "@/components/Hero";
import ServiceCard from "@/components/ServiceCard";
import TestimonialSlider from "@/components/TestimonialSlider";
import { Button } from "@/components/ui/button";
import { ArrowRight, CheckCircle, Heart, Shield, Sparkles, Star, Users, Zap } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function Home() {
	const services = [
		{
			title: "Soin énergétique complet",
			description: "Rééquilibrage global de vos énergies pour retrouver harmonie et vitalité",
			duration: "1h30",
			price: "85",
			features: [
				"Analyse énergétique approfondie",
				"Nettoyage et harmonisation",
				"Conseils personnalisés",
				"Suivi post-séance",
			],
			href: "/reservation/soin-energetique",
			icon: <Sparkles className="w-8 h-8 text-brand-gold" />,
		},
		{
			title: "Coaching bien-être",
			description: "Accompagnement personnalisé pour développer votre potentiel et votre épanouissement",
			duration: "1h",
			price: "65",
			features: [
				"Définition d'objectifs clairs",
				"Techniques de développement personnel",
				"Plan d'action personnalisé",
				"Suivi régulier",
			],
			href: "/reservation/coaching-bien-etre",
			icon: <Heart className="w-8 h-8 text-brand-gold" />,
		},
		{
			title: "Harmonisation chakras",
			description: "Équilibrage de vos centres énergétiques pour une circulation optimale",
			duration: "1h",
			price: "70",
			features: [
				"Diagnostic des 7 chakras principaux",
				"Techniques de rééquilibrage",
				"Méditation guidée",
				"Conseils d'entretien",
			],
			href: "/reservation/harmonisation-chakras",
			icon: <Zap className="w-8 h-8 text-brand-gold" />,
		},
	];

	const values = [
		{
			icon: <Heart className="w-8 h-8 text-brand-gold" />,
			title: "Bienveillance",
			description: "Un accompagnement dans le respect et l'écoute de votre rythme",
		},
		{
			icon: <Shield className="w-8 h-8 text-brand-gold" />,
			title: "Confidentialité",
			description: "Un espace sécurisé où vous pouvez vous exprimer en toute confiance",
		},
		{
			icon: <Sparkles className="w-8 h-8 text-brand-gold" />,
			title: "Authenticité",
			description: "Une approche sincère pour révéler votre véritable essence",
		},
		{
			icon: <Users className="w-8 h-8 text-brand-gold" />,
			title: "Personnalisation",
			description: "Chaque accompagnement est unique et adapté à vos besoins",
		},
	];

	return (
		<div className="overflow-hidden bg-gradient-to-br from-brand-cream via-white to-brand-sand/30 relative min-h-screen">
			{/* Global background pattern */}
			<div className="fixed inset-0 opacity-15 pointer-events-none">
				<div className="absolute top-1/4 left-1/4 w-96 h-96 bg-brand-gold rounded-full blur-3xl animate-gentle-pulse"></div>
				<div
					className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-brand-blue rounded-full blur-3xl animate-gentle-pulse"
					style={{ animationDelay: "2s" }}
				></div>
				<div
					className="absolute top-1/2 left-1/2 w-64 h-64 bg-brand-blue-variant rounded-full blur-2xl animate-gentle-pulse -translate-x-1/2 -translate-y-1/2"
					style={{ animationDelay: "4s" }}
				></div>
				<div
					className="absolute top-10 right-10 w-32 h-32 bg-brand-gold rounded-full blur-2xl animate-gentle-pulse"
					style={{ animationDelay: "1s" }}
				></div>
				<div
					className="absolute bottom-10 left-10 w-40 h-40 bg-brand-blue rounded-full blur-3xl animate-gentle-pulse"
					style={{ animationDelay: "3s" }}
				></div>
			</div>

			{/* Hero Section */}
			<Hero />

			{/* Values Section */}
			<section className="py-24 relative">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<div className="text-center mb-20">
						<div className="inline-flex items-center px-6 py-3 rounded-full bg-brand-gold/15 border-2 border-brand-gold/30 mb-8 shadow-lg backdrop-blur-sm">
							<Star className="w-6 h-6 text-brand-gold mr-3" />
							<span className="text-lg font-dm-serif text-brand-gold font-bold">Mes valeurs</span>
						</div>
						<h2 className="font-early-sunday text-4xl md:text-5xl font-bold text-brand-olive mb-6">
							Un accompagnement basé sur la confiance
						</h2>
						<p className="font-sans text-xl text-brand-olive/90 max-w-2xl mx-auto font-medium leading-relaxed">
							Chaque séance est un moment privilégié où vous êtes accueilli(e) dans un espace de
							bienveillance et de respect absolu.
						</p>
					</div>

					<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-10">
						{values.map((value, index) => (
							<div
								key={index}
								className="text-center group hover:scale-105 transition-all duration-300 bg-white/90 backdrop-blur-sm p-8 rounded-2xl shadow-lg hover:shadow-2xl border border-brand-gold/10 hover:border-brand-gold/20"
							>
								<div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-brand-gold/20 to-brand-gold/10 rounded-full flex items-center justify-center group-hover:shadow-lg transition-all duration-300 group-hover:scale-110">
									{value.icon}
								</div>
								<h3 className="font-dm-serif text-xl font-bold text-brand-olive mb-4">{value.title}</h3>
								<p className="font-sans text-brand-olive/80 text-base leading-relaxed font-medium">
									{value.description}
								</p>
							</div>
						))}
					</div>
				</div>
			</section>

			{/* Services Section */}
			<section className="py-24 relative">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<div className="text-center mb-20">
						<div className="inline-flex items-center px-6 py-3 rounded-full bg-brand-blue/15 border-2 border-brand-blue/30 mb-8 shadow-lg backdrop-blur-sm">
							<Sparkles className="w-6 h-6 text-brand-blue mr-3" />
							<span className="text-lg font-dm-serif text-brand-blue font-bold">Mes services</span>
						</div>
						<h2 className="font-early-sunday text-4xl md:text-5xl font-bold text-brand-olive mb-6">
							Des soins adaptés à vos besoins
						</h2>
						<p className="font-sans text-xl text-brand-olive/90 max-w-2xl mx-auto font-medium leading-relaxed">
							Découvrez mes différentes approches pour vous accompagner vers un mieux-être durable et
							authentique.
						</p>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 mb-12">
						{services.map((service, index) => (
							<ServiceCard key={index} {...service} />
						))}
					</div>

					<div className="text-center">
						<Button
							asChild
							size="lg"
							className="bg-brand-gold hover:bg-brand-gold-dark text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group text-lg shadow-lg"
						>
							<Link href="/reservation" className="flex items-center">
								Voir tous mes services
								<ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
							</Link>
						</Button>
					</div>
				</div>
			</section>

			{/* About Preview */}
			<section className="py-24 relative overflow-hidden">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<div className="grid lg:grid-cols-2 gap-16 items-center text-center lg:text-left">
						<div className="relative flex justify-center lg:block mb-8 lg:mb-0">
							<div className="aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-brand-blue/15 to-brand-gold/15 inline-block shadow-2xl border-2 border-white/20 backdrop-blur-sm">
								<Image
									src="/placeholder.svg?key=ww41z"
									alt="Laurence Gémin"
									width={500}
									height={500}
									className="object-cover w-full h-full"
								/>
							</div>
							<div className="absolute -bottom-6 -right-6 w-28 h-28 bg-brand-gold/20 backdrop-blur-sm rounded-full flex items-center justify-center shadow-xl border-2 border-white/30">
								<Sparkles className="w-10 h-10 text-brand-gold animate-pulse" />
							</div>
						</div>

						<div className="flex flex-col items-center lg:items-start">
							<div className="inline-flex items-center px-6 py-3 rounded-full bg-brand-gold/15 border-2 border-brand-gold/30 mb-8 shadow-lg backdrop-blur-sm">
								<Heart className="w-6 h-6 text-brand-gold mr-3" />
								<span className="text-lg font-dm-serif text-brand-gold font-bold">Mon parcours</span>
							</div>

							<h2 className="font-early-sunday text-4xl md:text-5xl font-bold text-brand-olive mb-8">
								Une passion devenue mission
							</h2>

							<div className="space-y-6 mb-10">
								<p className="font-sans text-brand-olive/90 leading-relaxed text-lg font-medium">
									Depuis plus de 10 ans, j'accompagne les personnes dans leur quête d'équilibre et de
									bien-être. Mon approche holistique combine techniques énergétiques traditionnelles
									et méthodes de coaching modernes.
								</p>

								<p className="font-sans text-brand-olive/90 leading-relaxed text-lg font-medium">
									Certifiée en soins énergétiques et formée aux techniques de libération émotionnelle,
									je vous guide avec bienveillance vers votre épanouissement personnel.
								</p>
							</div>

							<div className="space-y-4 mb-10 w-full flex flex-col items-center lg:items-start">
								{[
									"Praticienne certifiée en soins énergétiques",
									"Formation en coaching de vie et PNL",
									"Spécialisée en libération émotionnelle",
									"Plus de 500 accompagnements réalisés",
								].map((item, index) => (
									<div
										key={index}
										className="flex items-center bg-white/80 backdrop-blur-sm px-5 py-4 rounded-xl shadow-md border border-brand-gold/10 hover:border-brand-gold/20 transition-all duration-300"
									>
										<CheckCircle className="w-6 h-6 text-brand-gold mr-4 flex-shrink-0" />
										<span className="font-sans text-brand-olive/90 font-medium text-lg">
											{item}
										</span>
									</div>
								))}
							</div>

							<Button
								asChild
								size="lg"
								variant="outline"
								className="border-2 border-brand-blue text-brand-blue hover:bg-brand-blue hover:text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-lg text-lg backdrop-blur-sm bg-white/50"
							>
								<Link href="/about">Découvrir mon histoire</Link>
							</Button>
						</div>
					</div>
				</div>
			</section>

			{/* Testimonials Section */}
			<section className="py-24 relative">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<div className="text-center mb-20">
						<div className="inline-flex items-center px-6 py-3 rounded-full bg-brand-blue/15 border-2 border-brand-blue/30 mb-8 shadow-lg backdrop-blur-sm">
							<Users className="w-6 h-6 text-brand-blue mr-3" />
							<span className="text-lg font-dm-serif text-brand-blue font-bold">Témoignages</span>
						</div>
						<h2 className="font-early-sunday text-4xl md:text-5xl font-bold text-brand-olive mb-6">
							Ils ont retrouvé leur équilibre
						</h2>
						<p className="font-sans text-xl text-brand-olive/90 max-w-2xl mx-auto font-medium leading-relaxed">
							Découvrez les témoignages de personnes qui ont fait confiance à mon accompagnement pour
							transformer leur vie.
						</p>
					</div>

					<TestimonialSlider />

					<div className="text-center mt-12">
						<Button
							asChild
							variant="outline"
							size="lg"
							className="border-2 border-brand-blue text-brand-blue hover:bg-brand-blue hover:text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-lg text-lg backdrop-blur-sm bg-white/50"
						>
							<Link href="/testimonials">Voir tous les témoignages</Link>
						</Button>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-24 relative overflow-hidden">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
					<div className="bg-white/95 backdrop-blur-md rounded-3xl p-10 md:p-16 shadow-2xl border-2 border-brand-gold/30 hover:border-brand-gold/40 transition-all duration-300">
						<div className="w-20 h-20 mx-auto mb-8 bg-gradient-to-br from-brand-gold/20 to-brand-gold/10 rounded-full flex items-center justify-center shadow-lg">
							<Sparkles className="w-12 h-12 text-brand-gold" />
						</div>

						<h2 className="font-early-sunday text-4xl md:text-5xl font-bold text-brand-olive mb-8">
							Prêt(e) à commencer votre transformation ?
						</h2>

						<p className="font-sans text-xl text-brand-olive/90 mb-12 max-w-2xl mx-auto font-medium leading-relaxed">
							Offrez-vous un moment privilégié pour reconnecter avec votre essence et révéler votre
							potentiel authentique.
						</p>

						<div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
							<Button
								asChild
								size="lg"
								className="bg-brand-gold hover:bg-brand-gold-dark text-white font-sans font-bold px-12 py-4 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group text-lg shadow-lg"
							>
								<Link href="/reservation" className="flex items-center">
									Prendre rendez-vous
									<ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
								</Link>
							</Button>

							<Button
								asChild
								variant="outline"
								size="lg"
								className="border-2 border-brand-blue text-brand-blue bg-brand-cream hover:bg-brand-blue hover:text-white font-sans font-bold px-12 py-4 rounded-full transition-all duration-300 hover:shadow-lg text-lg"
							>
								<Link href="/reservation">Découvrir les services</Link>
							</Button>
						</div>

						<p className="font-sans text-lg text-brand-olive/70 mt-8 font-medium">
							Premier échange téléphonique gratuit • Paiement sécurisé • Satisfaction garantie
						</p>
					</div>
				</div>
			</section>
		</div>
	);
}

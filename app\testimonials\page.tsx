import FAQSection from "@/components/FAQSection";
import TestimonialSlider from "@/components/TestimonialSlider";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Heart, Star, Users } from "lucide-react";
import Link from "next/link";

export default function Testimonials() {
	const stats = [
		{
			number: "500+",
			label: "Accompagnements réalisés",
			icon: <Users className="w-8 h-8 text-brand-gold" />,
		},
		{
			number: "98%",
			label: "Clients satisfaits",
			icon: <Star className="w-8 h-8 text-brand-gold" />,
		},
		{
			number: "4.9/5",
			label: "Note moyenne",
			icon: <Heart className="w-8 h-8 text-brand-gold" />,
		},
	];

	return (
		<div className="pt-20 bg-gradient-to-br from-brand-cream via-white to-brand-sand/30 relative min-h-screen">
			{/* Global background pattern */}
			<div className="fixed inset-0 opacity-15 pointer-events-none">
				<div className="absolute top-1/4 left-1/4 w-96 h-96 bg-brand-gold rounded-full blur-3xl animate-gentle-pulse"></div>
				<div
					className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-brand-blue rounded-full blur-3xl animate-gentle-pulse"
					style={{ animationDelay: "2s" }}
				></div>
				<div
					className="absolute top-1/2 left-1/2 w-64 h-64 bg-brand-blue-variant rounded-full blur-2xl animate-gentle-pulse -translate-x-1/2 -translate-y-1/2"
					style={{ animationDelay: "4s" }}
				></div>
				<div
					className="absolute top-10 right-10 w-32 h-32 bg-brand-gold rounded-full blur-2xl animate-gentle-pulse"
					style={{ animationDelay: "1s" }}
				></div>
				<div
					className="absolute bottom-10 left-10 w-40 h-40 bg-brand-blue rounded-full blur-3xl animate-gentle-pulse"
					style={{ animationDelay: "3s" }}
				></div>
			</div>

			{/* Hero Section */}
			<section className="py-20 relative">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<div className="inline-flex items-center px-6 py-3 rounded-full bg-brand-gold/15 border-2 border-brand-gold/30 mb-8 shadow-lg backdrop-blur-sm">
						<Users className="w-5 h-5 text-brand-gold mr-3" />
						<span className="text-lg font-dm-serif text-brand-gold font-bold">Témoignages</span>
					</div>
					<h1 className="font-new-york text-4xl md:text-5xl font-bold text-olive mb-6">
						Ils ont retrouvé leur équilibre
					</h1>

					<p className="font-doulos text-xl text-olive/80 leading-relaxed mb-8 max-w-2xl mx-auto">
						Découvrez les témoignages authentiques de personnes qui ont fait confiance à mon accompagnement
						pour transformer leur vie et révéler leur potentiel.
					</p>

					<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
						<Button
							asChild
							size="lg"
							className="bg-brand-gold hover:bg-brand-gold-dark text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group text-lg shadow-lg"
						>
							<Link href="/reservation" className="flex items-center">
								Prendre rendez-vous
								<ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
							</Link>
						</Button>

						<Button
							asChild
							variant="outline"
							size="lg"
							className="border-2 border-brand-blue text-brand-blue hover:bg-brand-blue hover:text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-lg text-lg backdrop-blur-sm bg-white/50"
						>
							<Link href="/services">Découvrir les services</Link>
						</Button>
					</div>
				</div>
			</section>

			{/* Stats Section */}
			<section className="py-16 bg-white/80">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="grid md:grid-cols-3 gap-8 text-center">
						{stats.map((stat, index) => (
							<div key={index} className="group">
								<div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-brand-gold/10 to-brand-gold/5 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
									{stat.icon}
								</div>
								<div className="font-new-york text-3xl font-bold text-olive mb-2">{stat.number}</div>
								<p className="font-doulos text-olive/70">{stat.label}</p>
							</div>
						))}
					</div>
				</div>
			</section>

			{/* Testimonial Slider */}
			<section className="py-20 relative">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">Témoignages</h2>
						<p className="font-doulos text-xl text-olive/80">
							Des transformations authentiques et durables
						</p>
					</div>

					<TestimonialSlider />
				</div>
			</section>

			{/* Separator */}
			<section className="py-12 relative">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<div className="flex items-center justify-center">
						<div className="flex-1 h-px bg-gradient-to-r from-transparent via-brand-gold/30 to-transparent"></div>
						<div className="px-6">
							<div className="w-3 h-3 bg-brand-gold/20 rounded-full"></div>
						</div>
						<div className="flex-1 h-px bg-gradient-to-r from-transparent via-brand-gold/30 to-transparent"></div>
					</div>
				</div>
			</section>

			{/* Video Testimonials Placeholder */}
			<section className="py-20 relative">
				<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-brand-olive mb-4">
							Témoignages vidéo
						</h2>
						<p className="font-doulos text-xl text-brand-olive/80">Écoutez directement leurs expériences</p>
					</div>

					<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
						{[1, 2, 3].map((index) => (
							<Card
								key={index}
								className="group bg-gradient-to-br from-white via-white to-brand-sand/10 border-2 border-brand-sand/30 hover:border-brand-gold/50 transition-all duration-300 hover:shadow-2xl hover:-translate-y-3 backdrop-blur-sm hover:bg-gradient-to-br hover:from-white hover:via-brand-gold/5 hover:to-brand-gold/10"
							>
								<CardContent className="p-0">
									<div className="aspect-video bg-gradient-to-br from-brand-sand/20 to-brand-gold/15 rounded-t-lg flex items-center justify-center border-b border-brand-gold/20">
										<div className="text-center">
											<div className="w-20 h-20 bg-gradient-to-br from-brand-gold/20 to-brand-gold/10 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg border border-brand-gold/20">
												<div className="w-0 h-0 border-l-[10px] border-l-brand-gold border-y-[8px] border-y-transparent ml-1"></div>
											</div>
											<p className="font-doulos text-brand-olive/60 text-lg font-medium">
												Témoignage vidéo #{index}
											</p>
										</div>
									</div>
									<div className="p-6">
										<h3 className="font-new-york text-lg font-semibold text-brand-olive mb-2">
											Témoignage de Marie
										</h3>
										<p className="font-doulos text-brand-olive/70 text-base">
											Soin énergétique complet • 3 min
										</p>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* FAQ Section */}
			<FAQSection
				faqs={[
					{
						question: "Comment se déroule une première séance ?",
						answer: "La première séance commence toujours par un échange pour comprendre vos besoins et attentes. Je vous explique ensuite ma méthode et nous procédons au soin adapté à votre situation. Chaque séance est unique et personnalisée.",
					},
					{
						question: "Les soins à distance sont-ils aussi efficaces ?",
						answer: "Absolument ! L'énergie n'a pas de frontières. Mes clients à distance obtiennent les mêmes résultats qu'en présentiel. La connexion énergétique se fait naturellement, peu importe la distance physique.",
					},
					{
						question: "Combien de séances sont nécessaires ?",
						answer: "Cela dépend de votre situation et de vos objectifs. Certaines personnes ressentent des changements dès la première séance, d'autres préfèrent un accompagnement sur plusieurs mois. Nous adaptons ensemble selon vos besoins.",
					},
					{
						question: "Y a-t-il des contre-indications ?",
						answer: "Les soins énergétiques sont doux et naturels, sans contre-indications. Ils complètent parfaitement un suivi médical traditionnel sans jamais s'y substituer. Je respecte toujours vos traitements en cours.",
					},
				]}
				description="Les réponses aux questions que vous vous posez"
			/>

			{/* CTA Section */}
			<section className="py-20 bg-gradient-to-br from-gold/10 to-sand/20">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 md:p-12 shadow-xl border border-sand/30">
						<Heart className="w-12 h-12 text-brand-gold mx-auto mb-6" />

						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
							Prêt(e) à écrire votre propre témoignage ?
						</h2>

						<p className="font-doulos text-xl text-olive/80 mb-8 max-w-2xl mx-auto">
							Rejoignez les centaines de personnes qui ont déjà transformé leur vie. Votre parcours vers
							le mieux-être commence par un simple premier pas.
						</p>

						<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
							<Button
								asChild
								size="lg"
								className="bg-brand-gold hover:bg-brand-gold-dark text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group text-lg shadow-lg"
							>
								<Link href="/reservation" className="flex items-center">
									Prendre rendez-vous
									<ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
								</Link>
							</Button>

							<Button
								asChild
								variant="outline"
								size="lg"
								className="border-2 border-brand-blue text-brand-blue hover:bg-brand-blue hover:text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-lg text-lg backdrop-blur-sm bg-white/50"
							>
								<Link href="/services">Découvrir les services</Link>
							</Button>
						</div>

						<p className="font-doulos text-base text-olive/60 mt-6">
							Premier échange téléphonique gratuit • Accompagnement personnalisé • Résultats durables
						</p>
					</div>
				</div>
			</section>
		</div>
	);
}

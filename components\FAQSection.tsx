"use client";

import { Card, CardContent } from "@/components/ui/card";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useState } from "react";

interface FAQItem {
	question: string;
	answer: string;
}

interface FAQSectionProps {
	faqs: FAQItem[];
	title?: string;
	description?: string;
}

const FAQSection = ({ faqs, title = "Questions fréquentes", description }: FAQSectionProps) => {
	const [openItems, setOpenItems] = useState<number[]>([]);

	const toggleItem = (index: number) => {
		setOpenItems((prev) => (prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]));
	};

	return (
		<section className="py-20 relative">
			<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
				<div className="text-center mb-16">
					<h2 className="font-new-york text-3xl md:text-4xl font-bold text-brand-olive mb-4">{title}</h2>
					{description && <p className="font-doulos text-lg text-brand-olive/80">{description}</p>}
				</div>

				<div className="space-y-4">
					{faqs.map((faq, index) => (
						<Card
							key={index}
							className="group bg-gradient-to-br from-white via-white to-brand-sand/10 border-2 border-brand-sand/30 hover:border-brand-gold/50 transition-all duration-300 hover:shadow-2xl hover:-translate-y-3 backdrop-blur-sm hover:bg-gradient-to-br hover:from-white hover:via-brand-gold/5 hover:to-brand-gold/10 overflow-hidden"
						>
							<CardContent className="p-0">
								<button
									onClick={() => toggleItem(index)}
									className="w-full p-6 text-left flex items-center justify-between hover:bg-white/20 transition-colors duration-200"
								>
									<h3 className="font-new-york text-lg font-semibold text-brand-olive pr-4">
										{faq.question}
									</h3>
									<div className="flex-shrink-0">
										{openItems.includes(index) ? (
											<ChevronUp className="w-5 h-5 text-brand-gold" />
										) : (
											<ChevronDown className="w-5 h-5 text-brand-gold" />
										)}
									</div>
								</button>

								<div
									className={`transition-all duration-300 ease-in-out ${
										openItems.includes(index) ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
									} overflow-hidden`}
								>
									<div className="px-6 pb-6">
										<p className="font-doulos text-lg text-brand-olive/80 leading-relaxed">
											{faq.answer}
										</p>
									</div>
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		</section>
	);
};

export default FAQSection;

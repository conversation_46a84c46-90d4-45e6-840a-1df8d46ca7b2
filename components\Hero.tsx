"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Sparkles } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import HeroCarousel from "./HeroCarousel";

const Hero = () => {
	const [scrollY, setScrollY] = useState(0);

	useEffect(() => {
		const handleScroll = () => setScrollY(window.scrollY);
		window.addEventListener("scroll", handleScroll);
		return () => window.removeEventListener("scroll", handleScroll);
	}, []);

	return (
		<section className="relative min-h-screen flex items-end justify-center overflow-hidden mt-20">
			{/* Enhanced Floating Elements with Parallax */}
			<div
				className="absolute top-20 left-10 animate-float z-10"
				style={{ transform: `translateY(${scrollY * 0.2}px)` }}
			>
				<Sparkles className="w-8 h-8 text-brand-gold/60" />
			</div>
			<div
				className="absolute bottom-32 right-16 animate-float z-10"
				style={{
					animationDelay: "1s",
					transform: `translateY(${scrollY * -0.1}px)`,
				}}
			>
				<Sparkles className="w-6 h-6 text-brand-gold/70" />
			</div>
			<div
				className="absolute top-1/3 right-20 animate-float z-10"
				style={{
					animationDelay: "2s",
					transform: `translateY(${scrollY * 0.15}px)`,
				}}
			>
				<Sparkles className="w-4 h-4 text-brand-gold/50" />
			</div>

			{/* Right Half Floating Elements - Spread Across Entire Right Side */}

			{/* Far Right Edge Elements */}
			<div
				className="absolute top-24 right-4 animate-drift z-10"
				style={{
					animationDelay: "0.5s",
					transform: `translateY(${scrollY * 0.25}px)`,
				}}
			>
				<div className="w-12 h-12 rounded-full bg-brand-gold/20 backdrop-blur-sm border border-brand-gold/30 flex items-center justify-center animate-glow-pulse">
					<Sparkles className="w-5 h-5 text-brand-gold" />
				</div>
			</div>

			<div
				className="absolute top-40 right-12 animate-float z-10"
				style={{
					animationDelay: "1.5s",
					transform: `translateY(${scrollY * -0.2}px)`,
				}}
			>
				<div className="w-8 h-8 rounded-full bg-brand-gold/15 backdrop-blur-sm border border-brand-gold/20 flex items-center justify-center">
					<div className="w-3 h-3 bg-brand-gold rounded-full animate-gentle-pulse"></div>
				</div>
			</div>

			{/* Mid-Right Elements (Behind Portrait) */}
			<div
				className="absolute top-60 right-32 animate-float-rotate z-5"
				style={{
					animationDelay: "2.5s",
					transform: `translateY(${scrollY * 0.1}px)`,
				}}
			>
				<div className="w-16 h-16 rounded-full bg-gradient-to-br from-brand-gold/10 to-brand-gold/5 backdrop-blur-sm border border-brand-gold/20 flex items-center justify-center">
					<Sparkles className="w-6 h-6 text-brand-gold/80" />
				</div>
			</div>

			{/* Center-Right Elements (Behind Portrait) */}
			<div
				className="absolute top-16 right-64 animate-drift z-5"
				style={{
					animationDelay: "6s",
					transform: `translateY(${scrollY * 0.3}px)`,
				}}
			>
				<div className="w-6 h-6 bg-brand-gold/10 rounded-full border-2 border-brand-gold/25 animate-gentle-pulse"></div>
			</div>

			<div
				className="absolute top-72 right-56 animate-float z-5"
				style={{
					animationDelay: "6.5s",
					transform: `translateY(${scrollY * -0.2}px)`,
				}}
			>
				<div className="w-14 h-14 rounded-full bg-gradient-to-br from-brand-gold/15 to-brand-gold/5 backdrop-blur-sm border border-brand-gold/30 flex items-center justify-center shadow-lg">
					<div className="w-6 h-6 bg-brand-gold/30 rounded-full animate-gentle-pulse"></div>
				</div>
			</div>

			{/* Wide Spread Elements */}
			<div
				className="absolute top-32 right-80 animate-float z-5"
				style={{
					animationDelay: "7s",
					transform: `translateY(${scrollY * 0.15}px)`,
				}}
			>
				<div className="w-10 h-10 bg-brand-gold/12 rounded-full border border-brand-gold/25 animate-gentle-pulse"></div>
			</div>

			<div
				className="absolute bottom-20 right-72 animate-drift z-5"
				style={{
					animationDelay: "7.5s",
					transform: `translateY(${scrollY * -0.3}px)`,
				}}
			>
				<div className="w-8 h-8 border border-brand-gold/40 rounded-full bg-brand-gold/10 backdrop-blur-sm animate-glow-pulse"></div>
			</div>

			{/* Bottom Right Elements */}
			<div
				className="absolute bottom-40 right-8 animate-float z-15"
				style={{
					animationDelay: "3s",
					transform: `translateY(${scrollY * -0.15}px)`,
				}}
			>
				<div className="w-10 h-10 rounded-full bg-brand-gold/25 backdrop-blur-sm border border-brand-gold/40 flex items-center justify-center shadow-lg">
					<div className="w-4 h-4 bg-brand-gold rounded-full animate-gentle-pulse"></div>
				</div>
			</div>

			<div
				className="absolute bottom-60 right-44 animate-float z-5"
				style={{
					animationDelay: "3.5s",
					transform: `translateY(${scrollY * 0.3}px)`,
				}}
			>
				<Sparkles className="w-5 h-5 text-brand-gold/60" />
			</div>

			{/* Mid-Bottom Elements */}
			<div
				className="absolute bottom-80 right-16 animate-drift z-15"
				style={{
					animationDelay: "8s",
					transform: `translateY(${scrollY * 0.2}px)`,
				}}
			>
				<div className="w-6 h-6 bg-brand-gold/18 rounded-full border border-brand-gold/35 animate-gentle-pulse"></div>
			</div>

			<div
				className="absolute bottom-32 right-60 animate-float z-5"
				style={{
					animationDelay: "8.5s",
					transform: `translateY(${scrollY * -0.25}px)`,
				}}
			>
				<div className="w-12 h-12 rounded-full bg-gradient-to-br from-brand-gold/12 to-brand-gold/6 backdrop-blur-sm border border-brand-gold/25 flex items-center justify-center">
					<Sparkles className="w-4 h-4 text-brand-gold/70" />
				</div>
			</div>

			{/* Decorative Pattern Elements - Spread Across Right Half */}
			<div
				className="absolute top-32 right-68 animate-drift z-5"
				style={{
					animationDelay: "4s",
					transform: `translateY(${scrollY * -0.25}px)`,
				}}
			>
				<div className="w-6 h-6 border-2 border-brand-gold/30 rounded-full animate-gentle-pulse"></div>
			</div>

			<div
				className="absolute top-80 right-20 animate-float z-15"
				style={{
					animationDelay: "4.5s",
					transform: `translateY(${scrollY * 0.2}px)`,
				}}
			>
				<div className="w-4 h-4 bg-brand-gold/20 rounded-full blur-sm animate-gentle-pulse"></div>
			</div>

			<div
				className="absolute bottom-80 right-52 animate-float-rotate z-5"
				style={{
					animationDelay: "5s",
					transform: `translateY(${scrollY * -0.1}px)`,
				}}
			>
				<div className="w-8 h-8 border border-brand-gold/25 rounded-full bg-brand-gold/5 backdrop-blur-sm"></div>
			</div>

			{/* Wide Spread Pattern Elements */}
			<div
				className="absolute top-48 right-76 animate-float z-5"
				style={{
					animationDelay: "9s",
					transform: `translateY(${scrollY * 0.15}px)`,
				}}
			>
				<div className="w-3 h-3 bg-brand-gold/30 rounded-full animate-gentle-pulse"></div>
			</div>

			<div
				className="absolute bottom-24 right-24 animate-drift z-15"
				style={{
					animationDelay: "9.5s",
					transform: `translateY(${scrollY * -0.3}px)`,
				}}
			>
				<div className="w-5 h-5 border border-brand-gold/40 rounded-full bg-brand-gold/10 backdrop-blur-sm animate-glow-pulse"></div>
			</div>

			<div
				className="absolute top-96 right-64 animate-float z-5"
				style={{
					animationDelay: "10s",
					transform: `translateY(${scrollY * 0.25}px)`,
				}}
			>
				<div className="w-7 h-7 bg-gradient-to-br from-brand-gold/20 to-brand-gold/10 rounded-full border border-brand-gold/30 animate-gentle-pulse"></div>
			</div>

			{/* Far Right Edge Small Elements */}
			<div
				className="absolute top-64 right-6 animate-float z-15"
				style={{
					animationDelay: "10.5s",
					transform: `translateY(${scrollY * -0.2}px)`,
				}}
			>
				<div className="w-4 h-4 bg-brand-gold/25 rounded-full animate-gentle-pulse"></div>
			</div>

			<div
				className="absolute bottom-96 right-10 animate-drift z-15"
				style={{
					animationDelay: "11s",
					transform: `translateY(${scrollY * 0.3}px)`,
				}}
			>
				<Sparkles className="w-3 h-3 text-brand-gold/50" />
			</div>

			{/* Connecting Lines/Paths - Spread Across Right Half */}
			<div
				className="absolute top-36 right-48 w-16 h-0.5 bg-gradient-to-r from-brand-gold/20 to-transparent animate-gentle-pulse z-3"
				style={{
					animationDelay: "11.5s",
					transform: `rotate(45deg) translateY(${scrollY * 0.1}px)`,
				}}
			></div>

			<div
				className="absolute bottom-52 right-36 w-12 h-0.5 bg-gradient-to-r from-transparent to-brand-gold/25 animate-gentle-pulse z-3"
				style={{
					animationDelay: "12s",
					transform: `rotate(-30deg) translateY(${scrollY * -0.15}px)`,
				}}
			></div>

			<div
				className="absolute top-88 right-72 w-10 h-0.5 bg-gradient-to-r from-brand-gold/15 to-transparent animate-gentle-pulse z-3"
				style={{
					animationDelay: "12.5s",
					transform: `rotate(60deg) translateY(${scrollY * 0.2}px)`,
				}}
			></div>

			{/* Enhanced Background Patterns - Spread Across Right Half */}

			{/* Far Right Background Elements */}
			<div className="absolute top-20 right-16 w-32 h-32 bg-gradient-to-br from-brand-gold/5 to-transparent rounded-full blur-2xl animate-gentle-pulse z-0"></div>
			<div
				className="absolute top-60 right-4 w-24 h-24 bg-gradient-to-br from-brand-gold/8 to-transparent rounded-full blur-xl animate-gentle-pulse z-0"
				style={{ animationDelay: "2s" }}
			></div>
			<div
				className="absolute bottom-40 right-12 w-40 h-40 bg-gradient-to-br from-brand-gold/3 to-transparent rounded-full blur-3xl animate-gentle-pulse z-0"
				style={{ animationDelay: "4s" }}
			></div>

			{/* Mid-Right Background Elements */}
			<div
				className="absolute top-44 right-40 w-20 h-20 bg-gradient-to-br from-brand-gold/6 to-transparent rounded-full blur-xl animate-gentle-pulse z-0"
				style={{ animationDelay: "6s" }}
			></div>
			<div
				className="absolute bottom-64 right-56 w-28 h-28 bg-gradient-to-br from-brand-gold/4 to-transparent rounded-full blur-2xl animate-gentle-pulse z-0"
				style={{ animationDelay: "8s" }}
			></div>
			<div
				className="absolute top-80 right-48 w-16 h-16 bg-gradient-to-br from-brand-gold/7 to-transparent rounded-full blur-lg animate-gentle-pulse z-0"
				style={{ animationDelay: "10s" }}
			></div>

			{/* Wide Spread Background Elements */}
			<div
				className="absolute top-36 right-72 w-36 h-36 bg-gradient-to-br from-brand-gold/4 to-transparent rounded-full blur-3xl animate-gentle-pulse z-0"
				style={{ animationDelay: "12s" }}
			></div>
			<div
				className="absolute bottom-20 right-64 w-32 h-32 bg-gradient-to-br from-brand-gold/5 to-transparent rounded-full blur-2xl animate-gentle-pulse z-0"
				style={{ animationDelay: "14s" }}
			></div>
			<div
				className="absolute top-72 right-80 w-20 h-20 bg-gradient-to-br from-brand-gold/6 to-transparent rounded-full blur-xl animate-gentle-pulse z-0"
				style={{ animationDelay: "16s" }}
			></div>

			{/* Organic Shape Patterns - Spread Wide */}
			<div
				className="absolute top-28 right-60 w-36 h-24 bg-gradient-to-br from-brand-gold/2 to-transparent rounded-full blur-3xl animate-gentle-pulse z-0"
				style={{
					animationDelay: "18s",
					transform: `rotate(25deg) translateY(${scrollY * 0.05}px)`,
				}}
			></div>
			<div
				className="absolute bottom-32 right-28 w-44 h-20 bg-gradient-to-br from-brand-gold/3 to-transparent rounded-full blur-2xl animate-gentle-pulse z-0"
				style={{
					animationDelay: "20s",
					transform: `rotate(-15deg) translateY(${scrollY * -0.08}px)`,
				}}
			></div>
			<div
				className="absolute top-52 right-84 w-28 h-40 bg-gradient-to-br from-brand-gold/2 to-transparent rounded-full blur-3xl animate-gentle-pulse z-0"
				style={{
					animationDelay: "22s",
					transform: `rotate(45deg) translateY(${scrollY * 0.03}px)`,
				}}
			></div>

			{/* Content */}
			<div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				{/* Desktop Grid Layout */}
				<div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center min-h-[80vh]">
					{/* Text Content */}
					<div className="animate-fade-in text-center order-1 lg:order-1">
						{/* Badge */}
						<div className="inline-flex items-center px-6 py-3 rounded-full bg-brand-gold/15 border-2 border-brand-gold/30 mb-8 shadow-lg">
							<Sparkles className="w-6 h-6 text-brand-gold mr-3" />
							<span className="text-lg font-dm-serif text-brand-gold font-bold tracking-wide">
								Coach certifiée en soins énergétiques
							</span>
						</div>

						{/* Main Heading */}
						<h1 className="font-early-sunday text-4xl sm:text-5xl lg:text-6xl font-bold text-brand-olive mb-8 leading-tight">
							Retrouvez votre
							<span className="block text-brand-gold font-dm-serif italic">équilibre intérieur</span>
						</h1>

						{/* Subtitle */}
						<p className="font-sans text-xl sm:text-2xl text-brand-olive/90 mb-10 max-w-2xl mx-auto leading-relaxed font-medium">
							Accompagnement personnalisé en soins énergétiques et coaching bien-être pour révéler votre
							potentiel et harmoniser votre être dans sa globalité.
						</p>

						{/* Quote */}
						<blockquote className="font-dm-serif italic text-2xl text-brand-gold-dark mb-12 max-w-xl mx-auto font-bold">
							"Chaque être porte en lui une lumière unique qui ne demande qu'à rayonner"
						</blockquote>

						{/* CTA Buttons */}
						<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
							<Button
								asChild
								size="lg"
								className="bg-brand-gold hover:bg-brand-gold-dark text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group text-lg"
							>
								<Link href="/services" className="flex items-center">
									Découvrir mes services
									<ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
								</Link>
							</Button>

							<Button
								asChild
								variant="outline"
								size="lg"
								className="border-2 border-brand-blue text-brand-blue bg-brand-cream hover:bg-brand-blue hover:text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-lg text-lg"
							>
								<Link href="/about">Mon parcours</Link>
							</Button>
						</div>

						{/* Trust Indicators */}
						<div className="mt-20 flex flex-col sm:flex-row items-center justify-center gap-6 sm:gap-8">
							<div className="flex items-center">
								<div className="w-3 h-3 bg-brand-gold rounded-full mr-3"></div>
								<span className="font-doulos text-base sm:text-lg text-brand-olive/70 font-medium">
									Certifiée praticienne énergétique
								</span>
							</div>
							<div className="flex items-center">
								<div className="w-3 h-3 bg-brand-gold rounded-full mr-3"></div>
								<span className="font-doulos text-base sm:text-lg text-brand-olive/70 font-medium">
									Plus de 500 accompagnements
								</span>
							</div>
							<div className="flex items-center">
								<div className="w-3 h-3 bg-brand-gold rounded-full mr-3"></div>
								<span className="font-doulos text-base sm:text-lg text-brand-olive/70 font-medium">
									Approche holistique personnalisée
								</span>
							</div>
						</div>
					</div>

					{/* Portrait Carousel */}
					<div className="animate-fade-in order-2 lg:order-2 flex justify-center lg:justify-end items-end h-full">
						<div style={{ transform: `translateY(${scrollY * 0.05}px)` }}>
							<HeroCarousel />
						</div>
					</div>
				</div>
			</div>
		</section>
	);
};

export default Hero;

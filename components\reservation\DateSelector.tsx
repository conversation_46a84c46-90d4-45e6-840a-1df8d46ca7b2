"use client";

import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect, useState } from "react";

interface DateSelectorProps {
	selectedDate: string;
	onDateSelect: (date: string) => void;
}

export default function DateSelector({ selectedDate, onDateSelect }: DateSelectorProps) {
	const [currentMonth, setCurrentMonth] = useState(new Date());
	const [availableDates, setAvailableDates] = useState<string[]>([]);

	useEffect(() => {
		// TODO: connect backend here
		// Generate mock available dates (next 30 days, excluding weekends and some random dates)
		const dates: string[] = [];
		const today = new Date();

		for (let i = 1; i <= 30; i++) {
			const date = new Date(today);
			date.setDate(today.getDate() + i);

			// Skip weekends and some random dates to simulate unavailability
			if (date.getDay() !== 0 && date.getDay() !== 6 && Math.random() > 0.3) {
				dates.push(date.toISOString().split("T")[0]);
			}
		}

		setAvailableDates(dates);
	}, []);

	const getDaysInMonth = (date: Date) => {
		const year = date.getFullYear();
		const month = date.getMonth();
		const firstDay = new Date(year, month, 1);
		const lastDay = new Date(year, month + 1, 0);
		const daysInMonth = lastDay.getDate();
		const startingDayOfWeek = firstDay.getDay();

		const days = [];

		// Add empty cells for days before the first day of the month
		for (let i = 0; i < startingDayOfWeek; i++) {
			days.push(null);
		}

		// Add days of the month
		for (let day = 1; day <= daysInMonth; day++) {
			days.push(new Date(year, month, day));
		}

		return days;
	};

	const isDateAvailable = (date: Date | null) => {
		if (!date) return false;
		const dateString = date.toISOString().split("T")[0];
		return availableDates.includes(dateString);
	};

	const isDateSelected = (date: Date | null) => {
		if (!date) return false;
		const dateString = date.toISOString().split("T")[0];
		return selectedDate === dateString;
	};

	const handleDateClick = (date: Date | null) => {
		if (!date || !isDateAvailable(date)) return;
		const dateString = date.toISOString().split("T")[0];
		onDateSelect(dateString);
	};

	const goToPreviousMonth = () => {
		setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1));
	};

	const goToNextMonth = () => {
		setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1));
	};

	const monthNames = [
		"Janvier",
		"Février",
		"Mars",
		"Avril",
		"Mai",
		"Juin",
		"Juillet",
		"Août",
		"Septembre",
		"Octobre",
		"Novembre",
		"Décembre",
	];

	const dayNames = ["Dim", "Lun", "Mar", "Mer", "Jeu", "Ven", "Sam"];

	const days = getDaysInMonth(currentMonth);

	return (
		<div className="bg-white/50 rounded-lg p-4 border border-sand/30">
			{/* Month Navigation */}
			<div className="flex items-center justify-between mb-4">
				<Button
					type="button"
					variant="ghost"
					size="sm"
					onClick={goToPreviousMonth}
					className="text-olive hover:text-gold"
				>
					<ChevronLeft className="w-4 h-4" />
				</Button>

				<h3 className="font-new-york text-lg font-semibold text-olive">
					{monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
				</h3>

				<Button
					type="button"
					variant="ghost"
					size="sm"
					onClick={goToNextMonth}
					className="text-olive hover:text-gold"
				>
					<ChevronRight className="w-4 h-4" />
				</Button>
			</div>

			{/* Day Names */}
			<div className="grid grid-cols-7 gap-1 mb-2">
				{dayNames.map((day) => (
					<div key={day} className="text-center text-xs font-doulos text-olive/60 py-2">
						{day}
					</div>
				))}
			</div>

			{/* Calendar Days */}
			<div className="grid grid-cols-7 gap-1">
				{days.map((date, index) => (
					<button
						key={index}
						type="button"
						onClick={() => handleDateClick(date)}
						disabled={!isDateAvailable(date)}
						className={`
              aspect-square flex items-center justify-center text-sm font-doulos rounded-lg transition-all duration-200
              ${!date ? "invisible" : ""}
              ${
					isDateAvailable(date)
						? isDateSelected(date)
							? "bg-brand-gold text-white shadow-md"
							: "bg-white/80 text-brand-olive hover:bg-brand-gold/10 hover:text-brand-gold border border-brand-sand/30"
						: "bg-gray-100 text-gray-400 cursor-not-allowed"
				}
            `}
					>
						{date?.getDate()}
					</button>
				))}
			</div>

			<div className="mt-4 text-center">
				<p className="font-doulos text-olive/60 text-xs">
					Les créneaux disponibles sont affichés en blanc. Cliquez pour sélectionner.
				</p>
			</div>
		</div>
	);
}
